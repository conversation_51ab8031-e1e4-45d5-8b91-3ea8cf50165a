# Test the Advice API

### Get all advice (should be empty initially)
GET http://localhost:8081/api/advice

### Create a new advice
POST http://localhost:8081/api/advice
Content-Type: application/json

{
  "message": "Always write clean, readable code"
}

### Create another advice
POST http://localhost:8081/api/advice
Content-Type: application/json

{
  "message": "Test your code thoroughly"
}

### Get all advice (should now have 2 items)
GET http://localhost:8081/api/advice

### Get advice by ID
GET http://localhost:8081/api/advice/1

### Update advice
PUT http://localhost:8081/api/advice/1
Content-Type: application/json

{
  "message": "Always write clean, readable, and maintainable code"
}

### Get updated advice
GET http://localhost:8081/api/advice/1

### Delete advice
DELETE http://localhost:8081/api/advice/2

### Get all advice (should now have 1 item)
GET http://localhost:8081/api/advice

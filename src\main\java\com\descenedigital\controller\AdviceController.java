package com.descenedigital.controller;

import com.descenedigital.model.Advice;
import com.descenedigital.service.AdviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/advice")
public class AdviceController {

    @Autowired
    private AdviceService adviceService;

    @GetMapping
    public List<Advice> getAllAdvice() {
        return adviceService.getAllAdvice();
    }

    @GetMapping("/{id}")
    public ResponseEntity<Advice> getAdviceById(@PathVariable Long id) {
        Optional<Advice> advice = adviceService.getAdviceById(id);
        return advice.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @PostMapping
    public Advice createAdvice(@RequestBody Advice advice) {
        return adviceService.saveAdvice(advice);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Advice> updateAdvice(@PathVariable Long id, @RequestBody Advice adviceDetails) {
        Optional<Advice> advice = adviceService.getAdviceById(id);
        if (advice.isPresent()) {
            Advice existingAdvice = advice.get();
            existingAdvice.setMessage(adviceDetails.getMessage());
            return ResponseEntity.ok(adviceService.saveAdvice(existingAdvice));
        }
        return ResponseEntity.notFound().build();
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteAdvice(@PathVariable Long id) {
        adviceService.deleteAdvice(id);
        return ResponseEntity.ok().build();
    }
}
package com.descenedigital.service;

import com.descenedigital.model.Advice;
import com.descenedigital.repo.AdviceRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class AdviceService {

    @Autowired
    private AdviceRepo adviceRepo;

    public List<Advice> getAllAdvice() {
        return adviceRepo.findAll();
    }

    public Optional<Advice> getAdviceById(Long id) {
        return adviceRepo.findById(id);
    }

    public Advice saveAdvice(Advice advice) {
        return adviceRepo.save(advice);
    }

    public void deleteAdvice(Long id) {
        adviceRepo.deleteById(id);
    }
}